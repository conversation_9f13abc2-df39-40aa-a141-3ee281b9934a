// Custom hook for Trading Session Management

import { useCallback, useEffect, useRef } from 'react';
import { useTradingSessionStore } from '@/stores';
import { CSVParseResult, CandleData, TickData } from '@/types/trading';
import { TimeframeAggregator } from '@/utils/timeframeAggregator';
import { getBidAskFromCandle, getBidAskFromTick } from '@/utils/tradingCalculations';

export const useTradingSession = () => {
  const store = useTradingSessionStore();
  const playbackIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const timeframeAggregatorRef = useRef<TimeframeAggregator | null>(null);

  // Data loading and processing
  const handleDataLoaded = useCallback((result: CSVParseResult) => {
    let candleData: CandleData[] = [];
    let baseData: CandleData[] = [];

    if (result.dataType === 'candle') {
      candleData = result.candleData || [];
      baseData = candleData;
    } else if (result.dataType === 'tick' && result.tickData) {
      // Create timeframe aggregator for tick data
      timeframeAggregatorRef.current = new TimeframeAggregator();
      
      // Generate M1 candles from tick data as base
      baseData = timeframeAggregatorRef.current.aggregateToTimeframe(result.tickData, 'M1');
      candleData = baseData;
    }

    // Set precision based on data
    const precision = result.precision || 5;
    
    // Update store with new data
    store.setData(candleData, baseData, result.tickData);
    store.setSymbol(result.symbol || 'Unknown');
    store.setDataType(result.dataType);
    store.setPrecision(precision);
    store.setPlaybackSpeed(result.dataType === 'tick' ? 100 : 1000);
    store.setShowImporter(false);
  }, [store]);

  // Timeframe switching
  const switchTimeframe = useCallback((newTimeframe: string) => {
    if (!timeframeAggregatorRef.current || !store.tickData) {
      // For regular candle data, just update timeframe
      store.setTimeframe(newTimeframe as any);
      return;
    }

    // For tick data, aggregate to new timeframe
    const aggregatedData = timeframeAggregatorRef.current.aggregateToTimeframe(
      store.tickData,
      newTimeframe as any
    );
    
    store.setData(aggregatedData, store.baseData, store.tickData);
    store.setTimeframe(newTimeframe as any);
  }, [store]);

  // Playback control
  const startPlayback = useCallback(() => {
    if (playbackIntervalRef.current) {
      clearInterval(playbackIntervalRef.current);
    }

    store.setIsPlaying(true);

    const speed = store.playbackSpeed === 'realtime' ? 100 : store.playbackSpeed;
    
    playbackIntervalRef.current = setInterval(() => {
      const currentState = useTradingSessionStore.getState();
      const maxIndex = Math.max(0, currentState.data.length - 1);
      
      if (currentState.currentIndex >= maxIndex) {
        stopPlayback();
        return;
      }
      
      store.stepForward();
    }, speed);
  }, [store]);

  const stopPlayback = useCallback(() => {
    if (playbackIntervalRef.current) {
      clearInterval(playbackIntervalRef.current);
      playbackIntervalRef.current = null;
    }
    store.setIsPlaying(false);
  }, [store]);

  const togglePlayback = useCallback(() => {
    if (store.isPlaying) {
      stopPlayback();
    } else {
      startPlayback();
    }
  }, [store.isPlaying, startPlayback, stopPlayback]);

  // Market data updates
  const updateCurrentMarketData = useCallback(() => {
    const currentData = store.data[store.currentIndex];
    if (!currentData) return;

    let bid: number, ask: number, spread: number;

    if (store.dataType === 'tick' && store.tickData) {
      // For tick data, get bid/ask from current tick
      const currentTick = store.tickData[store.currentIndex];
      if (currentTick) {
        const bidAsk = getBidAskFromTick(currentTick);
        bid = bidAsk.bid;
        ask = bidAsk.ask;
        spread = bidAsk.spread;
      } else {
        // Fallback to candle data
        const bidAsk = getBidAskFromCandle(currentData);
        bid = bidAsk.bid;
        ask = bidAsk.ask;
        spread = bidAsk.spread;
      }
    } else {
      // For candle data
      const bidAsk = getBidAskFromCandle(currentData);
      bid = bidAsk.bid;
      ask = bidAsk.ask;
      spread = bidAsk.spread;
    }

    store.updateMarketData(bid, ask, spread);
  }, [store]);

  // Update market data when current index changes
  useEffect(() => {
    updateCurrentMarketData();
  }, [store.currentIndex, updateCurrentMarketData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (playbackIntervalRef.current) {
        clearInterval(playbackIntervalRef.current);
      }
    };
  }, []);

  // Computed values
  const totalItems = store.dataType === 'tick' && store.tickData 
    ? store.tickData.length 
    : store.data.length;

  const hasData = store.data.length > 0 || (store.tickData?.length || 0) > 0;

  const canStepForward = store.currentIndex < Math.max(0, totalItems - 1);
  const canStepBackward = store.currentIndex > 0;

  return {
    // State
    ...store,
    
    // Computed values
    totalItems,
    hasData,
    canStepForward,
    canStepBackward,
    
    // Actions
    handleDataLoaded,
    switchTimeframe,
    startPlayback,
    stopPlayback,
    togglePlayback,
    updateCurrentMarketData
  };
};
