// Custom hook for Chart Interactions

import { useCallback, useRef } from 'react';
import { useChartInteractionStore } from '@/stores';
import { ChartOrderPlacement, DragCallbacks } from '@/types/trading';
import { IChartApi, MouseEventParams, Time } from 'lightweight-charts';

interface UseChartInteractionProps {
  onChartOrderPlace?: (orderData: ChartOrderPlacement) => void;
  dragCallbacks?: DragCallbacks;
}

export const useChartInteraction = ({ 
  onChartOrderPlace, 
  dragCallbacks 
}: UseChartInteractionProps = {}) => {
  const store = useChartInteractionStore();
  const chartRef = useRef<IChartApi | null>(null);
  const isDraggingRef = useRef(false);
  const dragStartPriceRef = useRef<number | null>(null);

  // Chart reference management
  const setChartRef = useCallback((chart: IChartApi | null) => {
    chartRef.current = chart;
  }, []);

  // Context menu handling
  const handleRightClick = useCallback((param: MouseEventParams) => {
    if (!param.point || !param.time) return;

    const price = param.seriesData?.get(param.seriesData.keys().next().value)?.value as number;
    if (!price) return;

    store.showContextMenu(
      param.point.x,
      param.point.y,
      price,
      param.time as number
    );
  }, [store]);

  const handleContextMenuAction = useCallback((action: string, price: number, time: number) => {
    store.hideContextMenu();

    if (!onChartOrderPlace) return;

    switch (action) {
      case 'buy':
        onChartOrderPlace({
          type: 'buy',
          price,
          time
        });
        break;
      case 'sell':
        onChartOrderPlace({
          type: 'sell',
          price,
          time
        });
        break;
      case 'buy-limit':
        onChartOrderPlace({
          type: 'buy-limit',
          price,
          time
        });
        break;
      case 'sell-limit':
        onChartOrderPlace({
          type: 'sell-limit',
          price,
          time
        });
        break;
      case 'buy-stop':
        onChartOrderPlace({
          type: 'buy-stop',
          price,
          time
        });
        break;
      case 'sell-stop':
        onChartOrderPlace({
          type: 'sell-stop',
          price,
          time
        });
        break;
    }
  }, [store, onChartOrderPlace]);

  // Drag and drop handling
  const handleMouseDown = useCallback((param: MouseEventParams) => {
    if (!store.enableDragAndDrop || !param.point) return;

    const price = param.seriesData?.get(param.seriesData.keys().next().value)?.value as number;
    if (!price) return;

    // Check if clicking on an order or position line
    // This would need to be implemented based on the specific chart setup
    // For now, we'll use a simplified approach

    isDraggingRef.current = true;
    dragStartPriceRef.current = price;
    store.startDrag();
  }, [store]);

  const handleMouseMove = useCallback((param: MouseEventParams) => {
    if (!isDraggingRef.current || !dragStartPriceRef.current || !param.point) return;

    const currentPrice = param.seriesData?.get(param.seriesData.keys().next().value)?.value as number;
    if (!currentPrice) return;

    // Handle drag callbacks if provided
    if (dragCallbacks?.onDragMove) {
      dragCallbacks.onDragMove(currentPrice);
    }
  }, [dragCallbacks]);

  const handleMouseUp = useCallback((param: MouseEventParams) => {
    if (!isDraggingRef.current || !dragStartPriceRef.current) return;

    const finalPrice = param.seriesData?.get(param.seriesData.keys().next().value)?.value as number;
    
    if (finalPrice && dragCallbacks?.onDragEnd) {
      dragCallbacks.onDragEnd(finalPrice);
    }

    // Reset drag state
    isDraggingRef.current = false;
    dragStartPriceRef.current = null;
    store.endDrag();
  }, [store, dragCallbacks]);

  // Fibonacci tools
  const startFibonacciDrawing = useCallback(() => {
    // This would be implemented based on the specific chart library
    // For now, we'll provide the interface
    console.log('Starting Fibonacci drawing mode');
  }, []);

  const addFibonacciRetracement = useCallback((
    startPrice: number, 
    endPrice: number, 
    startTime: number, 
    endTime: number
  ) => {
    store.addFibonacciLine(startPrice, endPrice, startTime, endTime);
  }, [store]);

  // Trend line tools
  const startTrendLineDrawing = useCallback(() => {
    console.log('Starting trend line drawing mode');
  }, []);

  const addTrendLine = useCallback((
    startPrice: number, 
    endPrice: number, 
    startTime: number, 
    endTime: number
  ) => {
    store.addTrendLine(startPrice, endPrice, startTime, endTime);
  }, [store]);

  // Chart event handlers setup
  const setupChartEventHandlers = useCallback((chart: IChartApi) => {
    if (!chart) return;

    // Subscribe to mouse events
    chart.subscribeClick(handleMouseDown);
    chart.subscribeCrosshairMove(handleMouseMove);
    
    // Note: TradingView Lightweight Charts doesn't have built-in right-click support
    // This would need to be implemented at the container level
    
    return () => {
      // Cleanup event listeners
      chart.unsubscribeClick(handleMouseDown);
      chart.unsubscribeCrosshairMove(handleMouseMove);
    };
  }, [handleMouseDown, handleMouseMove]);

  // Price line utilities
  const coordinateToPrice = useCallback((coordinate: number): number | null => {
    if (!chartRef.current) return null;
    
    // This would use the chart's coordinateToPrice method
    // Implementation depends on the specific chart setup
    return null;
  }, []);

  const priceToCoordinate = useCallback((price: number): number | null => {
    if (!chartRef.current) return null;
    
    // This would use the chart's priceToCoordinate method
    // Implementation depends on the specific chart setup
    return null;
  }, []);

  // Clear all drawing tools
  const clearAllDrawings = useCallback(() => {
    store.clearFibonacciLines();
    store.clearTrendLines();
  }, [store]);

  return {
    // State
    ...store,
    
    // Chart reference
    setChartRef,
    chartRef: chartRef.current,
    
    // Event handlers
    handleRightClick,
    handleContextMenuAction,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    setupChartEventHandlers,
    
    // Drawing tools
    startFibonacciDrawing,
    addFibonacciRetracement,
    startTrendLineDrawing,
    addTrendLine,
    clearAllDrawings,
    
    // Utilities
    coordinateToPrice,
    priceToCoordinate,
    
    // Store actions (direct access)
    showContextMenu: store.showContextMenu,
    hideContextMenu: store.hideContextMenu,
    startDrag: store.startDrag,
    endDrag: store.endDrag
  };
};
