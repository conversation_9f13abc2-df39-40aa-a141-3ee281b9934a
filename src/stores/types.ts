// Store types for Zustand state management

import { 
  TradingSession, 
  Order, 
  Position, 
  Account,
  CandleData,
  TickData,
  Timeframe,
  UpdateMode,
  DataType,
  ChartOrderPlacement,
  DragCallbacks,
  TradingSettings
} from '@/types/trading';

// Trading Session Store State
export interface TradingSessionState {
  // Session data
  symbol: string;
  data: CandleData[];
  baseData: CandleData[];
  tickData?: TickData[];
  dataType: DataType;
  
  // Playback state
  currentIndex: number;
  isPlaying: boolean;
  playbackSpeed: number | 'realtime';
  
  // Market data
  currentBid: number;
  currentAsk: number;
  spread: number;
  lastKnownBid: number;
  lastKnownAsk: number;
  precision?: number;
  
  // Display settings
  timeframe: Timeframe;
  updateMode: UpdateMode;
  
  // Settings
  settings: TradingSettings;
  
  // UI state
  showImporter: boolean;
  showSettings: boolean;
}

// Trading Session Store Actions
export interface TradingSessionActions {
  // Data management
  setData: (data: CandleData[], baseData: CandleData[], tickData?: TickData[]) => void;
  setSymbol: (symbol: string) => void;
  setDataType: (dataType: DataType) => void;
  setPrecision: (precision: number) => void;
  
  // Playback controls
  setCurrentIndex: (index: number) => void;
  setIsPlaying: (isPlaying: boolean) => void;
  setPlaybackSpeed: (speed: number | 'realtime') => void;
  stepForward: () => void;
  stepBackward: () => void;
  resetSession: () => void;
  
  // Market data updates
  updateMarketData: (bid: number, ask: number, spread: number) => void;
  setLastKnownPrices: (bid: number, ask: number) => void;
  
  // Display settings
  setTimeframe: (timeframe: Timeframe) => void;
  setUpdateMode: (updateMode: UpdateMode) => void;
  
  // Settings
  updateSettings: (settings: Partial<TradingSettings>) => void;
  
  // UI state
  setShowImporter: (show: boolean) => void;
  setShowSettings: (show: boolean) => void;
}

// Account Store State
export interface AccountState {
  account: Account;
  orders: Order[];
  positions: Position[];
  startBalance: number;
}

// Account Store Actions
export interface AccountActions {
  // Account management
  updateAccount: (account: Partial<Account>) => void;
  resetAccount: (balance: number) => void;
  
  // Order management
  addOrder: (order: Order) => void;
  updateOrder: (orderId: string, updates: Partial<Order>) => void;
  removeOrder: (orderId: string) => void;
  clearOrders: () => void;
  
  // Position management
  addPosition: (position: Position) => void;
  updatePosition: (positionId: string, updates: Partial<Position>) => void;
  removePosition: (positionId: string) => void;
  clearPositions: () => void;
  
  // Bulk operations
  processOrderExecution: (order: Order, executionPrice: number) => void;
  updatePositionPnL: (positionId: string, currentPrice: number) => void;
}

// Chart Interaction Store State
export interface ChartInteractionState {
  // Fibonacci tools
  fibonacciLines: Array<{
    id: string;
    startPrice: number;
    endPrice: number;
    startTime: number;
    endTime: number;
  }>;
  
  // Drawing tools
  trendLines: Array<{
    id: string;
    startPrice: number;
    endPrice: number;
    startTime: number;
    endTime: number;
  }>;
  
  // Drag and drop state
  isDragging: boolean;
  draggedOrderId?: string;
  draggedPositionId?: string;
  
  // Context menu state
  contextMenu: {
    visible: boolean;
    x: number;
    y: number;
    price: number;
    time: number;
  };
  
  // Chart settings
  enableFibonacci: boolean;
  enableDragAndDrop: boolean;
}

// Chart Interaction Store Actions
export interface ChartInteractionActions {
  // Fibonacci tools
  addFibonacciLine: (startPrice: number, endPrice: number, startTime: number, endTime: number) => void;
  removeFibonacciLine: (id: string) => void;
  clearFibonacciLines: () => void;
  
  // Trend lines
  addTrendLine: (startPrice: number, endPrice: number, startTime: number, endTime: number) => void;
  removeTrendLine: (id: string) => void;
  clearTrendLines: () => void;
  
  // Drag and drop
  startDrag: (orderId?: string, positionId?: string) => void;
  endDrag: () => void;
  
  // Context menu
  showContextMenu: (x: number, y: number, price: number, time: number) => void;
  hideContextMenu: () => void;
  
  // Chart settings
  setEnableFibonacci: (enable: boolean) => void;
  setEnableDragAndDrop: (enable: boolean) => void;
}

// Combined store types
export type TradingSessionStore = TradingSessionState & TradingSessionActions;
export type AccountStore = AccountState & AccountActions;
export type ChartInteractionStore = ChartInteractionState & ChartInteractionActions;
