// Chart Interaction Store using Zustand

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ChartInteractionStore } from './types';
import { createInitialChartInteractionState, generateId } from './utils';

export const useChartInteractionStore = create<ChartInteractionStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      ...createInitialChartInteractionState(),

      // Fibonacci tools actions
      addFibonacciLine: (startPrice: number, endPrice: number, startTime: number, endTime: number) => {
        const newLine = {
          id: generateId(),
          startPrice,
          endPrice,
          startTime,
          endTime
        };

        set(
          (state) => ({
            ...state,
            fibonacciLines: [...state.fibonacciLines, newLine]
          }),
          false,
          'addFibonacciLine'
        );
      },

      removeFibonacciLine: (id: string) => {
        set(
          (state) => ({
            ...state,
            fibonacciLines: state.fibonacciLines.filter(line => line.id !== id)
          }),
          false,
          'removeFibonacciLine'
        );
      },

      clearFibonacciLines: () => {
        set(
          (state) => ({ ...state, fibonacciLines: [] }),
          false,
          'clearFibonacciLines'
        );
      },

      // Trend lines actions
      addTrendLine: (startPrice: number, endPrice: number, startTime: number, endTime: number) => {
        const newLine = {
          id: generateId(),
          startPrice,
          endPrice,
          startTime,
          endTime
        };

        set(
          (state) => ({
            ...state,
            trendLines: [...state.trendLines, newLine]
          }),
          false,
          'addTrendLine'
        );
      },

      removeTrendLine: (id: string) => {
        set(
          (state) => ({
            ...state,
            trendLines: state.trendLines.filter(line => line.id !== id)
          }),
          false,
          'removeTrendLine'
        );
      },

      clearTrendLines: () => {
        set(
          (state) => ({ ...state, trendLines: [] }),
          false,
          'clearTrendLines'
        );
      },

      // Drag and drop actions
      startDrag: (orderId?: string, positionId?: string) => {
        set(
          (state) => ({
            ...state,
            isDragging: true,
            draggedOrderId: orderId,
            draggedPositionId: positionId
          }),
          false,
          'startDrag'
        );
      },

      endDrag: () => {
        set(
          (state) => ({
            ...state,
            isDragging: false,
            draggedOrderId: undefined,
            draggedPositionId: undefined
          }),
          false,
          'endDrag'
        );
      },

      // Context menu actions
      showContextMenu: (x: number, y: number, price: number, time: number) => {
        set(
          (state) => ({
            ...state,
            contextMenu: {
              visible: true,
              x,
              y,
              price,
              time
            }
          }),
          false,
          'showContextMenu'
        );
      },

      hideContextMenu: () => {
        set(
          (state) => ({
            ...state,
            contextMenu: {
              ...state.contextMenu,
              visible: false
            }
          }),
          false,
          'hideContextMenu'
        );
      },

      // Chart settings actions
      setEnableFibonacci: (enableFibonacci: boolean) => {
        set(
          (state) => ({ ...state, enableFibonacci }),
          false,
          'setEnableFibonacci'
        );
      },

      setEnableDragAndDrop: (enableDragAndDrop: boolean) => {
        set(
          (state) => ({ ...state, enableDragAndDrop }),
          false,
          'setEnableDragAndDrop'
        );
      }
    }),
    {
      name: 'chart-interaction-store',
      enabled: process.env.NODE_ENV === 'development'
    }
  )
);
