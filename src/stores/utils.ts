// Store utilities and helpers

import { DEFAULT_TRADING_SETTINGS } from '@/utils/tradingCalculations';
import { TradingSessionState, AccountState, ChartInteractionState } from './types';

// Initial state factories
export const createInitialTradingSessionState = (): TradingSessionState => ({
  symbol: '',
  data: [],
  baseData: [],
  tickData: undefined,
  dataType: 'candle',
  currentIndex: 0,
  isPlaying: false,
  playbackSpeed: 1000,
  currentBid: 0,
  currentAsk: 0,
  spread: 0,
  lastKnownBid: 0,
  lastKnownAsk: 0,
  precision: 5,
  timeframe: 'M1',
  updateMode: 'complete',
  settings: DEFAULT_TRADING_SETTINGS,
  showImporter: true,
  showSettings: false
});

export const createInitialAccountState = (initialBalance: number = 10000): AccountState => ({
  account: {
    balance: initialBalance,
    equity: initialBalance,
    margin: 0,
    freeMargin: initialBalance,
    marginLevel: 0,
    totalPnL: 0,
    totalCommission: 0
  },
  orders: [],
  positions: [],
  startBalance: initialBalance
});

export const createInitialChartInteractionState = (): ChartInteractionState => ({
  fibonacciLines: [],
  trendLines: [],
  isDragging: false,
  draggedOrderId: undefined,
  draggedPositionId: undefined,
  contextMenu: {
    visible: false,
    x: 0,
    y: 0,
    price: 0,
    time: 0
  },
  enableFibonacci: true,
  enableDragAndDrop: true
});

// Store persistence helpers (optional for future use)
export const STORAGE_KEYS = {
  TRADING_SESSION: 'trading-session-store',
  ACCOUNT: 'account-store',
  CHART_INTERACTION: 'chart-interaction-store'
} as const;

// Helper to generate unique IDs
export const generateId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};
