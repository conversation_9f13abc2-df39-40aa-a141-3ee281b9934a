// Trading Session Store using Zustand

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { TradingSessionStore } from './types';
import { createInitialTradingSessionState } from './utils';
import { CandleData, TickData, DataType, Timeframe, UpdateMode, TradingSettings } from '@/types/trading';

export const useTradingSessionStore = create<TradingSessionStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      ...createInitialTradingSessionState(),

      // Data management actions
      setData: (data: CandleData[], baseData: CandleData[], tickData?: TickData[]) => {
        set(
          (state) => ({
            ...state,
            data,
            baseData,
            tickData,
            currentIndex: 0,
            isPlaying: false
          }),
          false,
          'setData'
        );
      },

      setSymbol: (symbol: string) => {
        set(
          (state) => ({ ...state, symbol }),
          false,
          'setSymbol'
        );
      },

      setDataType: (dataType: DataType) => {
        set(
          (state) => ({ ...state, dataType }),
          false,
          'setDataType'
        );
      },

      setPrecision: (precision: number) => {
        set(
          (state) => ({ ...state, precision }),
          false,
          'setPrecision'
        );
      },

      // Playback control actions
      setCurrentIndex: (currentIndex: number) => {
        const state = get();
        const maxIndex = Math.max(0, state.data.length - 1);
        const clampedIndex = Math.max(0, Math.min(currentIndex, maxIndex));
        
        set(
          (state) => ({ ...state, currentIndex: clampedIndex }),
          false,
          'setCurrentIndex'
        );
      },

      setIsPlaying: (isPlaying: boolean) => {
        set(
          (state) => ({ ...state, isPlaying }),
          false,
          'setIsPlaying'
        );
      },

      setPlaybackSpeed: (playbackSpeed: number | 'realtime') => {
        set(
          (state) => ({ ...state, playbackSpeed }),
          false,
          'setPlaybackSpeed'
        );
      },

      stepForward: () => {
        const state = get();
        const maxIndex = Math.max(0, state.data.length - 1);
        if (state.currentIndex < maxIndex) {
          set(
            (state) => ({ ...state, currentIndex: state.currentIndex + 1 }),
            false,
            'stepForward'
          );
        }
      },

      stepBackward: () => {
        const state = get();
        if (state.currentIndex > 0) {
          set(
            (state) => ({ ...state, currentIndex: state.currentIndex - 1 }),
            false,
            'stepBackward'
          );
        }
      },

      resetSession: () => {
        set(
          (state) => ({
            ...state,
            currentIndex: 0,
            isPlaying: false
          }),
          false,
          'resetSession'
        );
      },

      // Market data actions
      updateMarketData: (currentBid: number, currentAsk: number, spread: number) => {
        set(
          (state) => ({
            ...state,
            currentBid,
            currentAsk,
            spread,
            lastKnownBid: currentBid || state.lastKnownBid,
            lastKnownAsk: currentAsk || state.lastKnownAsk
          }),
          false,
          'updateMarketData'
        );
      },

      setLastKnownPrices: (lastKnownBid: number, lastKnownAsk: number) => {
        set(
          (state) => ({ ...state, lastKnownBid, lastKnownAsk }),
          false,
          'setLastKnownPrices'
        );
      },

      // Display settings actions
      setTimeframe: (timeframe: Timeframe) => {
        set(
          (state) => ({ ...state, timeframe }),
          false,
          'setTimeframe'
        );
      },

      setUpdateMode: (updateMode: UpdateMode) => {
        set(
          (state) => ({ ...state, updateMode }),
          false,
          'setUpdateMode'
        );
      },

      // Settings actions
      updateSettings: (settingsUpdate: Partial<TradingSettings>) => {
        set(
          (state) => ({
            ...state,
            settings: { ...state.settings, ...settingsUpdate }
          }),
          false,
          'updateSettings'
        );
      },

      // UI state actions
      setShowImporter: (showImporter: boolean) => {
        set(
          (state) => ({ ...state, showImporter }),
          false,
          'setShowImporter'
        );
      },

      setShowSettings: (showSettings: boolean) => {
        set(
          (state) => ({ ...state, showSettings }),
          false,
          'setShowSettings'
        );
      }
    }),
    {
      name: 'trading-session-store',
      // Enable Redux DevTools for debugging
      enabled: process.env.NODE_ENV === 'development'
    }
  )
);
